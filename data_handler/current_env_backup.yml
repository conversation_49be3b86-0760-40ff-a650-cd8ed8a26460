name: msa
channels:
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - asttokens=3.0.0=pyhd8ed1ab_1
  - bzip2=1.0.8=h5eee18b_6
  - ca-certificates=2025.7.14=hbd8a1cb_0
  - comm=0.2.3=pyhe01879c_0
  - debugpy=1.8.15=py39haef64b4_0
  - decorator=5.2.1=pyhd8ed1ab_0
  - exceptiongroup=1.3.0=pyhd8ed1ab_0
  - executing=2.2.0=pyhd8ed1ab_0
  - expat=2.7.1=h6a678d5_0
  - importlib-metadata=8.7.0=pyhe01879c_1
  - ipykernel=6.30.0=pyh82676e8_0
  - ipython=8.18.1=pyh707e725_3
  - jedi=0.19.2=pyhd8ed1ab_1
  - jupyter_client=8.6.3=pyhd8ed1ab_1
  - jupyter_core=5.8.1=pyh31011fe_0
  - krb5=1.21.3=h723845a_3
  - ld_impl_linux-64=2.40=h12ee557_0
  - libedit=3.1.20250104=pl5321h7949ede_0
  - libffi=3.4.4=h6a678d5_1
  - libgcc=15.1.0=h767d61c_4
  - libgcc-ng=15.1.0=h69a702a_4
  - libgomp=15.1.0=h767d61c_4
  - libkrb5=1.21.3=h520c7b4_3
  - libsodium=1.0.20=h4ab18f5_0
  - libstdcxx=15.1.0=h8f9b012_4
  - libstdcxx-ng=11.2.0=h1234567_1
  - libxcb=1.17.0=h9b100fa_0
  - lmdb=0.9.31=hd590300_1
  - matplotlib-inline=0.1.7=pyhd8ed1ab_1
  - ncurses=6.5=h7934f7d_0
  - nest-asyncio=1.6.0=pyhd8ed1ab_1
  - openssl=3.5.1=h7b32b05_0
  - packaging=25.0=pyh29332c3_1
  - parso=0.8.4=pyhd8ed1ab_1
  - pexpect=4.9.0=pyhd8ed1ab_1
  - pickleshare=0.7.5=pyhd8ed1ab_1004
  - pip=25.1=pyhc872135_2
  - platformdirs=4.3.8=pyhe01879c_0
  - prompt-toolkit=3.0.51=pyha770c72_0
  - psutil=7.0.0=py39h8cd3c5a_0
  - pthread-stubs=0.3=h0ce48e5_1
  - ptyprocess=0.7.0=pyhd8ed1ab_1
  - pure_eval=0.2.3=pyhd8ed1ab_1
  - pygments=2.19.2=pyhd8ed1ab_0
  - python=3.9.23=he99959a_0
  - python-dateutil=2.9.0.post0=pyhe01879c_2
  - python_abi=3.9=2_cp39
  - pyzmq=27.0.0=py39h4e4fb57_0
  - readline=8.2=h5eee18b_0
  - setuptools=78.1.1=py39h06a4308_0
  - six=1.17.0=pyhe01879c_1
  - sqlite=3.50.2=hb25bd0a_1
  - stack_data=0.6.3=pyhd8ed1ab_1
  - tk=8.6.14=h993c535_1
  - tornado=6.5.1=py39h8cd3c5a_0
  - traitlets=5.14.3=pyhd8ed1ab_1
  - typing_extensions=4.14.1=pyhe01879c_0
  - wcwidth=0.2.13=pyhd8ed1ab_1
  - wheel=0.45.1=py39h06a4308_0
  - xorg-libx11=1.8.12=h9b100fa_1
  - xorg-libxau=1.0.12=h9b100fa_0
  - xorg-libxdmcp=1.1.5=h9b100fa_0
  - xorg-xorgproto=2024.1=h5eee18b_1
  - xz=5.6.4=h5eee18b_1
  - zeromq=4.3.5=h3b0a872_7
  - zipp=3.23.0=pyhd8ed1ab_0
  - zlib=1.2.13=h5eee18b_1
  - pip:
      - contourpy==1.3.0
      - cycler==0.12.1
      - filelock==3.13.1
      - fonttools==4.59.0
      - fsspec==2024.6.1
      - importlib-resources==6.5.2
      - jinja2==3.1.4
      - joblib==1.5.1
      - kiwisolver==1.4.7
      - markupsafe==2.1.5
      - matplotlib==3.8.0
      - mne==0.20.8
      - mpmath==1.3.0
      - networkx==3.2.1
      - numpy==1.26.3
      - nvidia-cublas-cu11==*********
      - nvidia-cuda-cupti-cu11==11.8.87
      - nvidia-cuda-nvrtc-cu11==11.8.89
      - nvidia-cuda-runtime-cu11==11.8.89
      - nvidia-cudnn-cu11==********
      - nvidia-cufft-cu11==*********
      - nvidia-curand-cu11==*********
      - nvidia-cusolver-cu11==*********
      - nvidia-cusparse-cu11==*********
      - nvidia-nccl-cu11==2.21.5
      - nvidia-nvtx-cu11==11.8.86
      - pandas==2.2.3
      - pillow==11.0.0
      - ptflops==0.7.3
      - pyparsing==3.2.3
      - pytz==2025.2
      - scikit-learn==1.2.2
      - scipy==1.13.1
      - sympy==1.13.3
      - threadpoolctl==3.6.0
      - torch==2.7.1+cu118
      - torchaudio==2.7.1+cu118
      - torchvision==0.22.1+cu118
      - tqdm==4.65.0
      - triton==3.3.1
      - typing-extensions==4.12.2
      - tzdata==2025.2
prefix: /media/main/ypf/miniconda3/envs/msa
